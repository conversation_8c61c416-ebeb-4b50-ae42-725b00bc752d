import { createRouter, createWebHistory, RouteRecordRaw } from 'vue-router';
import { setupRouterGuard } from './guard';

const routes: Array<RouteRecordRaw> = [
  { path: '/login', name: 'Login', component: () => import('../pages/LoginPage.vue') },
  { path: '/register', name: 'Register', component: () => import('../pages/RegisterPage.vue') },
  { path: '/', redirect: '/dashboard' },
  { 
    path: '/dashboard', 
    name: 'Dashboard', 
    component: () => import('../pages/dashboard/DashboardPage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/medicine',
    name: 'Medicine',
    component: () => import('../pages/medicine/MedicineList.vue'),
    meta: { requiresAuth: true, permission: 'medicine:view' }
  },
  {
    path: '/medicine/add',
    name: 'MedicineAdd',
    component: () => import('../pages/medicine/MedicineAdd.vue'),
    meta: { requiresAuth: true, permission: 'medicine:add' }
  },
  {
    path: '/inventory',
    name: 'Inventory',
    component: () => import('../pages/inventory/InventoryList.vue'),
    meta: { requiresAuth: true, permission: 'inventory:view' }
  },
  {
    path: '/inventory/in',
    name: 'InventoryIn',
    component: () => import('../pages/inventory/InventoryInPage.vue'),
    meta: { requiresAuth: true, permission: 'inventory:in' }
  },
  {
    path: '/inventory/out',
    name: 'InventoryOut',
    component: () => import('../pages/inventory/InventoryOutPage.vue'),
    meta: { requiresAuth: true, permission: 'inventory:out' }
  },
  {
    path: '/purchase',
    name: 'Purchase',
    component: () => import('../pages/purchase/PurchaseList.vue'),
    meta: { requiresAuth: true, permission: 'purchase:view' }
  },
  {
    path: '/purchase/add',
    name: 'PurchaseAdd',
    component: () => import('../pages/purchase/PurchaseAddPage.vue'),
    meta: { requiresAuth: true, permission: 'purchase:add' }
  },
  {
    path: '/sale',
    name: 'Sale',
    component: () => import('../pages/sale/SaleList.vue'),
    meta: { requiresAuth: true, permission: 'sale:view' }
  },
  {
    path: '/sale/add',
    name: 'SaleAdd',
    component: () => import('../pages/sale/SaleAddPage.vue'),
    meta: { requiresAuth: true, permission: 'sale:add' }
  },
  {
    path: '/sale/add',
    name: 'SaleAdd',
    component: () => import('../pages/sale/SaleAddPage.vue'),
    meta: { requiresAuth: true, permission: 'sale:add' }
  },
  { 
    path: '/user', 
    name: 'User', 
    component: () => import('../pages/user/UserList.vue'),
    meta: { requiresAuth: true, permission: 'user:view' }
  },
  { 
    path: '/role', 
    name: 'Role', 
    component: () => import('../pages/role/RoleList.vue'),
    meta: { requiresAuth: true, permission: 'role:view' }
  },
  { 
    path: '/supplier', 
    name: 'Supplier', 
    component: () => import('../pages/supplier/SupplierList.vue'),
    meta: { requiresAuth: true, permission: 'supplier:view' }
  },
  { 
    path: '/customer', 
    name: 'Customer', 
    component: () => import('../pages/customer/CustomerList.vue'),
    meta: { requiresAuth: true, permission: 'customer:view' }
  },
  { 
    path: '/report', 
    name: 'Report', 
    component: () => import('../pages/report/ReportDashboard.vue'),
    meta: { requiresAuth: true, permission: 'report:view' }
  },
  { 
    path: '/setting', 
    name: 'Setting', 
    component: () => import('../pages/setting/SettingList.vue'),
    meta: { requiresAuth: true, permission: 'setting:view' }
  },
  { 
    path: '/log', 
    name: 'Log', 
    component: () => import('../pages/log/LogList.vue'),
    meta: { requiresAuth: true, permission: 'log:view' }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('../pages/profile/ProfilePage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/change-password',
    name: 'ChangePassword',
    component: () => import('../pages/profile/ChangePasswordPage.vue'),
    meta: { requiresAuth: true }
  },
  {
    path: '/user-settings',
    name: 'UserSettings',
    component: () => import('../pages/profile/UserSettingsPage.vue'),
    meta: { requiresAuth: true }
  },
];

const router = createRouter({
  history: createWebHistory(),
  routes,
});

// 设置路由守卫
setupRouterGuard(router);

export default router;
