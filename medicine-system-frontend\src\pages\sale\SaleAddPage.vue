<template>
  <div class="sale-add-page">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="breadcrumb-section">
          <el-breadcrumb separator="/">
            <el-breadcrumb-item>
              <router-link to="/sale">销售管理</router-link>
            </el-breadcrumb-item>
            <el-breadcrumb-item>添加销售记录</el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        <h1 class="page-title">
          <el-icon class="title-icon"><Plus /></el-icon>
          添加销售记录
        </h1>
        <p class="page-description">填写以下信息来创建新的销售记录</p>
      </div>
    </div>

    <!-- 表单容器 -->
    <div class="form-container">
      <div class="form-card">
        <div class="card-header">
          <h3>销售信息</h3>
          <div class="card-actions">
            <el-button @click="handleCancel">
              <el-icon><ArrowLeft /></el-icon>
              返回列表
            </el-button>
          </div>
        </div>
        
        <div class="card-content">
          <SaleForm
            mode="add"
            @success="handleSuccess"
            @cancel="handleCancel"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router';
import { ElMessage } from 'element-plus';
import { Plus, ArrowLeft } from '@element-plus/icons-vue';
import SaleForm from './SaleForm.vue';

// 路由实例
const router = useRouter();

// 处理表单提交成功
const handleSuccess = () => {
  ElMessage.success('销售记录添加成功！');
  // 跳转回销售列表页面
  router.push('/sale');
};

// 处理取消操作
const handleCancel = () => {
  router.push('/sale');
};
</script>

<style scoped>
.sale-add-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 0;
}

.page-header {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.9) 0%, rgba(118, 75, 162, 0.9) 100%);
  backdrop-filter: blur(10px);
  padding: 2rem 0;
  color: white;
  position: relative;
  overflow: hidden;
}

.page-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
  pointer-events: none;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
  z-index: 1;
}

.breadcrumb-section {
  margin-bottom: 1rem;
}

.breadcrumb-section :deep(.el-breadcrumb__inner) {
  color: rgba(255, 255, 255, 0.8);
  font-weight: 500;
}

.breadcrumb-section :deep(.el-breadcrumb__inner:hover) {
  color: white;
}

.breadcrumb-section :deep(.el-breadcrumb__separator) {
  color: rgba(255, 255, 255, 0.6);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  margin: 0 0 0.5rem 0;
  display: flex;
  align-items: center;
  gap: 1rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.title-icon {
  font-size: 2.5rem;
  background: rgba(255, 255, 255, 0.2);
  padding: 0.5rem;
  border-radius: 12px;
  backdrop-filter: blur(10px);
}

.page-description {
  font-size: 1.1rem;
  margin: 0;
  opacity: 0.9;
  font-weight: 400;
}

.form-container {
  max-width: 1200px;
  margin: -2rem auto 0;
  padding: 0 2rem 2rem;
  position: relative;
  z-index: 2;
}

.form-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  overflow: hidden;
  transition: all 0.3s ease;
}

.form-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2rem 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
  margin-bottom: 2rem;
}

.card-header h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: #2c3e50;
  margin: 0;
}

.card-actions {
  display: flex;
  gap: 1rem;
}

.card-content {
  padding: 0 2rem 2rem;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-title {
    font-size: 2rem;
  }
  
  .title-icon {
    font-size: 2rem;
    padding: 0.4rem;
  }
  
  .header-content,
  .form-container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
  
  .card-header {
    flex-direction: column;
    gap: 1rem;
    align-items: flex-start;
  }
  
  .card-actions {
    width: 100%;
    justify-content: flex-end;
  }
}

/* 暗黑模式支持 */
body.dark .form-card {
  background: rgba(31, 31, 31, 0.95);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

body.dark .card-header h3 {
  color: #e6e6e6;
}

body.dark .card-header {
  border-bottom-color: rgba(255, 255, 255, 0.1);
}
</style>
